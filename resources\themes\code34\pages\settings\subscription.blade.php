<?php
    use function Laravel\Folio\{middleware, name};
    middleware('auth');
    name('settings.subscription');
?>

<x-layouts.app>
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-lg-12">
                <x-elements.back-button
                    text="Voltar às Configurações"
                    :href="route('settings')"
                    class="mb-4"
                />

                <div class="hero-heading-sec text-center mb-5">
                    <h2 class="text-white">
                        <span>Assinatura</span>
                    </h2>
                    <p class="text-white">Gerencie sua assinatura e métodos de pagamento.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="bg-white rounded p-5">
                    <h5 class="mb-4">Status da Assinatura</h5>
                    
                    @subscriber
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle mr-2"></i>
                            <strong>Assinatura Ativa</strong><br>
                            Você tem uma assinatura ativa com a função <strong>{{ auth()->user()->roles()->first()->name }}</strong>.
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <h6>Detalhes da Assinatura</h6>
                                <p><strong>Plano:</strong> {{ auth()->user()->roles()->first()->name }}</p>
                                <p><strong>Status:</strong> <span class="badge badge-success">Ativo</span></p>
                                @if(auth()->user()->subscription('default'))
                                    <p><strong>Próxima cobrança:</strong> {{ auth()->user()->subscription('default')->asStripeSubscription()->current_period_end ? \Carbon\Carbon::createFromTimestamp(auth()->user()->subscription('default')->asStripeSubscription()->current_period_end)->format('d/m/Y') : 'N/A' }}</p>
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            <strong>Sem Assinatura Ativa</strong><br>
                            Você não possui uma assinatura ativa. Faça upgrade para acessar recursos premium.
                        </div>
                        
                        <div class="text-center">
                            <a href="/pricing" class="btn-main bg-btn lnk">
                                Ver Planos <i class="fas fa-chevron-right fa-icon"></i>
                                <span class="circle"></span>
                            </a>
                        </div>
                    @endsubscriber
                </div>
            </div>
        </div>

        @subscriber
        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="bg-white rounded p-5">
                    <h5 class="mb-4">Gerenciar Assinatura</h5>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-primary btn-block" onclick="alert('Funcionalidade em desenvolvimento')">
                                <i class="fas fa-edit mr-2"></i> Alterar Plano
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-secondary btn-block" onclick="alert('Funcionalidade em desenvolvimento')">
                                <i class="fas fa-credit-card mr-2"></i> Métodos de Pagamento
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-info btn-block" onclick="alert('Funcionalidade em desenvolvimento')">
                                <i class="fas fa-file-invoice mr-2"></i> Histórico de Faturas
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-warning btn-block" onclick="alert('Funcionalidade em desenvolvimento')">
                                <i class="fas fa-pause mr-2"></i> Pausar Assinatura
                            </button>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <button class="btn btn-outline-danger" onclick="alert('Para cancelar sua assinatura, entre em contato com o suporte.')">
                            <i class="fas fa-times mr-2"></i> Cancelar Assinatura
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endsubscriber

        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="bg-white rounded p-4">
                    <h6>Precisa de Ajuda?</h6>
                    <p class="text-muted">Se você tiver dúvidas sobre sua assinatura ou precisar de suporte, entre em contato conosco.</p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                        <i class="fas fa-envelope mr-2"></i> Contatar Suporte
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
