<?php
    use function <PERSON><PERSON>\Folio\{middleware, name};
    middleware('auth');
    name('settings.security');
?>

<x-layouts.app>
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-lg-12">
                <x-elements.back-button
                    text="Voltar às Configurações"
                    :href="route('settings')"
                    class="mb-4"
                />

                <div class="hero-heading-sec text-center mb-5">
                    <h2 class="text-white">
                        <span>Configurações de Segurança</span>
                    </h2>
                    <p class="text-white">Gerencie sua senha e configurações de segurança da conta.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="bg-white rounded p-5">
                    <h5 class="mb-4"><PERSON><PERSON><PERSON></h5>
                    
                    <form method="POST" action="{{ route('settings.security') }}">
                        @csrf
                        @method('PUT')

                        <div class="form-group mb-3">
                            <label for="current_password" class="form-label"><PERSON><PERSON> Atual</label>
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                   id="current_password" name="current_password" required>
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="password" class="form-label">Nova Senha</label>
                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                   id="password" name="password" required>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-4">
                            <label for="password_confirmation" class="form-label">Confirmar Nova Senha</label>
                            <input type="password" class="form-control" 
                                   id="password_confirmation" name="password_confirmation" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn-main bg-btn lnk">
                                Alterar Senha <i class="fas fa-save fa-icon"></i>
                                <span class="circle"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-lg-8 mx-auto">
                <div class="bg-white rounded p-5">
                    <h5 class="mb-4">Configurações de Segurança</h5>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                        <div>
                            <h6 class="mb-1">Autenticação de Dois Fatores</h6>
                            <p class="text-muted mb-0">Adicione uma camada extra de segurança à sua conta.</p>
                        </div>
                        <div>
                            @if(auth()->user()->two_factor_secret)
                                <span class="badge badge-success">Ativado</span>
                                <a href="{{ route('user.two-factor-authentication') }}" class="btn btn-sm btn-outline-danger ml-2">Desativar</a>
                            @else
                                <span class="badge badge-secondary">Desativado</span>
                                <a href="{{ route('user.two-factor-authentication') }}" class="btn btn-sm btn-outline-primary ml-2">Ativar</a>
                            @endif
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                        <div>
                            <h6 class="mb-1">Sessões Ativas</h6>
                            <p class="text-muted mb-0">Gerencie onde você está logado.</p>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-warning" onclick="alert('Funcionalidade em desenvolvimento')">
                                Ver Sessões
                            </button>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                        <div>
                            <h6 class="mb-1">Excluir Conta</h6>
                            <p class="text-muted mb-0">Exclua permanentemente sua conta e todos os dados.</p>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-danger" onclick="alert('Para excluir sua conta, entre em contato com o suporte.')">
                                Excluir Conta
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
