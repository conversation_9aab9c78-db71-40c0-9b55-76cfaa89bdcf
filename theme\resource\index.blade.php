<!DOCTYPE html>
<html lang="pt-br" class="no-js">

<head>
    <meta charset="utf-8" />
    <title>
        @hasSection('title')
            @yield('title') - {{ config('app.name') }}
        @else
            {{ config('app.name') }} - {{ config('app.description') }}
        @endif
    </title>
    <meta name="description" content="Code 34, Soluções em Desenvolvimento de Software">
    <meta name="keywords" content="Code 34, desenvolviemnto de software, agência, marketing digital">
    <meta name="author" content="Code 34">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#322d97">
    <!--website-favicon-->
    <link href="{{ asset('images/favicon.ico') }}" rel="icon">
    <!--plugin-css-->
    <link href="{{ asset('site/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('site/css/plugin.min.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css" rel="stylesheet">
    <link
        href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Poppins:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <!-- template-style-->
    <link href="{{ asset('site/css/style.css') }}" rel="stylesheet">
    <link href="{{ asset('site/css/responsive.css') }}" rel="stylesheet">

    @stack('css')
    @yield('css')

</head>

<body class="active-dark">
    <!--Start Header -->
    @include('site.layouts.dark.partials._header')
    <!--End Header -->
    @yield('content')
    <!--Start Footer-->
    @include('site.layouts.dark.partials._footer')
    <!--End Footer-->
    <!--scroll to top-->
    <a id="scrollUp" href="#top"></a>
    <!-- js placed at the end of the document so the pages load faster -->
    <script src="{{ asset('site/js/vendor/modernizr-3.5.0.min.js') }}"></script>
    <script src="{{ asset('site/js/jquery.min.js') }}"></script>
    <script src="{{ asset('site/js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('site/js/popper.min.js') }}"></script>
    <script src="{{ asset('site/js/plugin.min.js') }}"></script>
    <!--common script file-->
    <script src="{{ asset('site/js/main.js') }}"></script>

    @stack('js')
    @yield('js')

    <script>
    </script>
</body>

</html>
