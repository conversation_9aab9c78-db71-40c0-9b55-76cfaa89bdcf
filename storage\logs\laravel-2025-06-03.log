[2025-06-03 20:50:46] local.ERROR: <PERSON>rror connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:46] local.INFO: Installed plugins: []  
[2025-06-03 20:50:48] local.ERROR: Error connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:48] local.INFO: Installed plugins: []  
[2025-06-03 20:50:49] local.ERROR: Error connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:49] local.INFO: Installed plugins: []  
[2025-06-03 20:50:51] local.ERROR: Error connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:51] local.INFO: Installed plugins: []  
[2025-06-03 20:50:58] local.INFO: Installed plugins: []  
[2025-06-03 20:51:04] local.INFO: Installed plugins: []  
[2025-06-03 20:51:06] local.INFO: Installed plugins: []  
[2025-06-03 20:51:30] local.INFO: Installed plugins: []  
[2025-06-03 20:52:30] local.INFO: Installed plugins: []  
[2025-06-03 20:52:30] local.INFO: Installed plugins: []  
[2025-06-03 20:53:30] local.INFO: Installed plugins: []  
[2025-06-03 20:53:30] local.INFO: Installed plugins: []  
[2025-06-03 20:54:30] local.INFO: Installed plugins: []  
[2025-06-03 20:55:30] local.INFO: Installed plugins: []  
[2025-06-03 20:55:30] local.INFO: Installed plugins: []  
[2025-06-03 20:56:30] local.INFO: Installed plugins: []  
[2025-06-03 20:56:30] local.INFO: Installed plugins: []  
[2025-06-03 20:57:30] local.INFO: Installed plugins: []  
[2025-06-03 20:57:30] local.INFO: Installed plugins: []  
[2025-06-03 20:58:30] local.INFO: Installed plugins: []  
[2025-06-03 20:58:30] local.INFO: Installed plugins: []  
[2025-06-03 20:58:30] local.INFO: Installed plugins: []  
[2025-06-03 20:59:30] local.INFO: Installed plugins: []  
[2025-06-03 21:00:30] local.INFO: Installed plugins: []  
[2025-06-03 21:01:30] local.INFO: Installed plugins: []  
[2025-06-03 21:01:30] local.INFO: Installed plugins: []  
[2025-06-03 21:02:30] local.INFO: Installed plugins: []  
[2025-06-03 21:02:30] local.INFO: Installed plugins: []  
[2025-06-03 21:03:30] local.INFO: Installed plugins: []  
[2025-06-03 21:03:30] local.INFO: Installed plugins: []  
[2025-06-03 21:04:30] local.INFO: Installed plugins: []  
[2025-06-03 21:05:30] local.INFO: Installed plugins: []  
[2025-06-03 21:05:30] local.INFO: Installed plugins: []  
[2025-06-03 21:06:30] local.INFO: Installed plugins: []  
[2025-06-03 21:06:30] local.INFO: Installed plugins: []  
[2025-06-03 21:07:30] local.INFO: Installed plugins: []  
[2025-06-03 21:07:30] local.INFO: Installed plugins: []  
[2025-06-03 21:08:30] local.INFO: Installed plugins: []  
[2025-06-03 21:08:30] local.INFO: Installed plugins: []  
[2025-06-03 21:08:30] local.INFO: Installed plugins: []  
[2025-06-03 21:09:30] local.INFO: Installed plugins: []  
[2025-06-03 21:09:30] local.INFO: Installed plugins: []  
[2025-06-03 21:10:30] local.INFO: Installed plugins: []  
[2025-06-03 21:10:30] local.INFO: Installed plugins: []  
[2025-06-03 21:11:30] local.INFO: Installed plugins: []  
[2025-06-03 21:12:30] local.INFO: Installed plugins: []  
[2025-06-03 21:12:30] local.INFO: Installed plugins: []  
[2025-06-03 21:13:30] local.INFO: Installed plugins: []  
[2025-06-03 21:13:30] local.INFO: Installed plugins: []  
[2025-06-03 21:14:30] local.INFO: Installed plugins: []  
[2025-06-03 21:14:30] local.INFO: Installed plugins: []  
[2025-06-03 21:15:30] local.INFO: Installed plugins: []  
[2025-06-03 21:16:30] local.INFO: Installed plugins: []  
[2025-06-03 21:16:30] local.INFO: Installed plugins: []  
[2025-06-03 21:17:30] local.INFO: Installed plugins: []  
[2025-06-03 21:17:30] local.INFO: Installed plugins: []  
[2025-06-03 21:18:30] local.INFO: Installed plugins: []  
[2025-06-03 21:18:30] local.INFO: Installed plugins: []  
[2025-06-03 21:18:30] local.INFO: Installed plugins: []  
[2025-06-03 21:19:30] local.INFO: Installed plugins: []  
[2025-06-03 21:19:30] local.INFO: Installed plugins: []  
[2025-06-03 21:20:30] local.INFO: Installed plugins: []  
[2025-06-03 21:20:43] local.INFO: Installed plugins: []  
[2025-06-03 21:21:03] local.INFO: Installed plugins: []  
[2025-06-03 21:21:16] local.INFO: Installed plugins: []  
[2025-06-03 21:21:17] local.INFO: Installed plugins: []  
[2025-06-03 21:21:30] local.INFO: Installed plugins: []  
[2025-06-03 21:21:38] local.INFO: Installed plugins: []  
[2025-06-03 21:21:39] local.INFO: Installed plugins: []  
[2025-06-03 21:21:40] local.INFO: Installed plugins: []  
[2025-06-03 21:21:52] local.INFO: Installed plugins: []  
[2025-06-03 21:21:53] local.INFO: Installed plugins: []  
[2025-06-03 21:21:55] local.INFO: Installed plugins: []  
[2025-06-03 21:22:16] local.INFO: Installed plugins: []  
[2025-06-03 21:22:18] local.INFO: Installed plugins: []  
[2025-06-03 21:22:30] local.INFO: Installed plugins: []  
[2025-06-03 21:23:30] local.INFO: Installed plugins: []  
[2025-06-03 21:24:30] local.INFO: Installed plugins: []  
[2025-06-03 21:24:30] local.INFO: Installed plugins: []  
[2025-06-03 21:25:25] local.INFO: Installed plugins: []  
[2025-06-03 21:25:30] local.INFO: Installed plugins: []  
[2025-06-03 21:25:31] local.INFO: Installed plugins: []  
[2025-06-03 21:25:31] local.INFO: Installed plugins: []  
[2025-06-03 21:25:33] local.INFO: Installed plugins: []  
[2025-06-03 21:25:35] local.INFO: Installed plugins: []  
[2025-06-03 21:25:51] local.INFO: Installed plugins: []  
[2025-06-03 21:25:52] local.INFO: Installed plugins: []  
[2025-06-03 21:25:52] local.INFO: Installed plugins: []  
[2025-06-03 21:26:02] local.INFO: Installed plugins: []  
[2025-06-03 21:26:07] local.INFO: Installed plugins: []  
[2025-06-03 21:26:08] local.INFO: Installed plugins: []  
[2025-06-03 21:26:08] local.INFO: Installed plugins: []  
[2025-06-03 21:26:15] local.INFO: Installed plugins: []  
[2025-06-03 21:26:16] local.INFO: Installed plugins: []  
[2025-06-03 21:26:21] local.INFO: Installed plugins: []  
[2025-06-03 21:26:30] local.INFO: Installed plugins: []  
[2025-06-03 21:27:30] local.INFO: Installed plugins: []  
[2025-06-03 21:27:30] local.INFO: Installed plugins: []  
[2025-06-03 21:28:30] local.INFO: Installed plugins: []  
[2025-06-03 21:28:31] local.INFO: Installed plugins: []  
[2025-06-03 21:28:31] local.INFO: Installed plugins: []  
[2025-06-03 21:29:30] local.INFO: Installed plugins: []  
[2025-06-03 21:29:30] local.INFO: Installed plugins: []  
[2025-06-03 21:30:30] local.INFO: Installed plugins: []  
[2025-06-03 21:30:30] local.INFO: Installed plugins: []  
[2025-06-03 21:31:30] local.INFO: Installed plugins: []  
[2025-06-03 21:31:30] local.INFO: Installed plugins: []  
[2025-06-03 21:32:30] local.INFO: Installed plugins: []  
[2025-06-03 21:33:30] local.INFO: Installed plugins: []  
[2025-06-03 21:33:30] local.INFO: Installed plugins: []  
[2025-06-03 21:34:30] local.INFO: Installed plugins: []  
[2025-06-03 21:34:30] local.INFO: Installed plugins: []  
[2025-06-03 21:35:30] local.INFO: Installed plugins: []  
[2025-06-03 21:36:30] local.INFO: Installed plugins: []  
[2025-06-03 21:36:30] local.INFO: Installed plugins: []  
[2025-06-03 21:37:30] local.INFO: Installed plugins: []  
[2025-06-03 21:37:30] local.INFO: Installed plugins: []  
[2025-06-03 21:38:30] local.INFO: Installed plugins: []  
[2025-06-03 21:38:30] local.INFO: Installed plugins: []  
[2025-06-03 21:39:30] local.INFO: Installed plugins: []  
[2025-06-03 21:39:30] local.INFO: Installed plugins: []  
[2025-06-03 21:40:30] local.INFO: Installed plugins: []  
[2025-06-03 21:40:30] local.INFO: Installed plugins: []  
[2025-06-03 21:41:30] local.INFO: Installed plugins: []  
[2025-06-03 21:41:30] local.INFO: Installed plugins: []  
[2025-06-03 21:42:31] local.INFO: Installed plugins: []  
[2025-06-03 21:42:31] local.INFO: Installed plugins: []  
[2025-06-03 21:43:31] local.INFO: Installed plugins: []  
[2025-06-03 21:44:30] local.INFO: Installed plugins: []  
[2025-06-03 21:45:31] local.INFO: Installed plugins: []  
[2025-06-03 21:45:31] local.INFO: Installed plugins: []  
[2025-06-03 21:46:31] local.INFO: Installed plugins: []  
[2025-06-03 21:47:31] local.INFO: Installed plugins: []  
[2025-06-03 21:48:30] local.INFO: Installed plugins: []  
[2025-06-03 21:48:31] local.INFO: Installed plugins: []  
[2025-06-03 21:48:31] local.INFO: Installed plugins: []  
[2025-06-03 21:49:31] local.INFO: Installed plugins: []  
[2025-06-03 21:50:31] local.INFO: Installed plugins: []  
[2025-06-03 21:51:31] local.INFO: Installed plugins: []  
[2025-06-03 21:52:31] local.INFO: Installed plugins: []  
[2025-06-03 21:52:31] local.INFO: Installed plugins: []  
[2025-06-03 21:53:31] local.INFO: Installed plugins: []  
[2025-06-03 21:53:31] local.INFO: Installed plugins: []  
[2025-06-03 21:54:31] local.INFO: Installed plugins: []  
[2025-06-03 21:54:31] local.INFO: Installed plugins: []  
[2025-06-03 21:55:31] local.INFO: Installed plugins: []  
[2025-06-03 21:55:31] local.INFO: Installed plugins: []  
[2025-06-03 21:56:31] local.INFO: Installed plugins: []  
[2025-06-03 21:56:31] local.INFO: Installed plugins: []  
[2025-06-03 21:57:31] local.INFO: Installed plugins: []  
[2025-06-03 21:57:31] local.INFO: Installed plugins: []  
[2025-06-03 21:58:30] local.INFO: Installed plugins: []  
[2025-06-03 21:58:31] local.INFO: Installed plugins: []  
[2025-06-03 21:58:31] local.INFO: Installed plugins: []  
[2025-06-03 21:59:31] local.INFO: Installed plugins: []  
[2025-06-03 21:59:31] local.INFO: Installed plugins: []  
[2025-06-03 21:59:42] local.INFO: Installed plugins: []  
[2025-06-03 22:00:31] local.INFO: Installed plugins: []  
[2025-06-03 22:00:36] local.INFO: Installed plugins: []  
[2025-06-03 22:00:49] local.INFO: Installed plugins: []  
[2025-06-03 22:00:57] local.INFO: Installed plugins: []  
[2025-06-03 22:01:04] local.INFO: Installed plugins: []  
[2025-06-03 22:01:11] local.INFO: Installed plugins: []  
[2025-06-03 22:01:11] local.INFO: Installed plugins: []  
[2025-06-03 22:01:12] local.INFO: Installed plugins: []  
[2025-06-03 22:01:13] local.ERROR: Method App\Filament\Pages\Themes::publishAssets does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method App\\Filament\\Pages\\Themes::publishAssets does not exist. at E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Component.php:136)
[stacktrace]
#0 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(85): Livewire\\Component->__call('publishAssets', Array)
#1 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(25): App\\Filament\\Pages\\Themes->installThemes()
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\Themes->mount()
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#8 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#9 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#10 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#11 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#12 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#13 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#14 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#15 E:\\projects\\My\\code34\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#16 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#17 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#18 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#20 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Themes), '__invoke')
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#77 {main}
"} 
[2025-06-03 22:01:15] local.INFO: Installed plugins: []  
[2025-06-03 22:01:20] local.INFO: Installed plugins: []  
[2025-06-03 22:01:24] local.INFO: Installed plugins: []  
[2025-06-03 22:01:31] local.INFO: Installed plugins: []  
[2025-06-03 22:01:31] local.INFO: Installed plugins: []  
[2025-06-03 22:01:42] local.ERROR: Maximum execution time of 30 seconds exceeded {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at E:\\projects\\My\\code34\\storage\\framework\\views\\5e8c24f9e8e354bfded7ce03625b600f.php:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 22:01:45] local.INFO: Installed plugins: []  
[2025-06-03 22:01:47] local.INFO: Installed plugins: []  
[2025-06-03 22:02:30] local.INFO: Installed plugins: []  
[2025-06-03 22:02:31] local.INFO: Installed plugins: []  
[2025-06-03 22:02:41] local.INFO: Installed plugins: []  
[2025-06-03 22:02:42] local.INFO: Installed plugins: []  
[2025-06-03 22:02:44] local.INFO: Installed plugins: []  
[2025-06-03 22:03:03] local.INFO: Installed plugins: []  
[2025-06-03 22:03:03] local.INFO: Installed plugins: []  
[2025-06-03 22:03:04] local.ERROR: Method App\Filament\Pages\Themes::publishAssets does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method App\\Filament\\Pages\\Themes::publishAssets does not exist. at E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Component.php:136)
[stacktrace]
#0 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(85): Livewire\\Component->__call('publishAssets', Array)
#1 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(25): App\\Filament\\Pages\\Themes->installThemes()
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\Themes->mount()
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#8 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#9 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#10 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#11 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#12 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#13 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#14 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#15 E:\\projects\\My\\code34\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#16 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#17 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#18 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#20 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Themes), '__invoke')
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#77 {main}
"} 
[2025-06-03 22:03:05] local.INFO: Installed plugins: []  
[2025-06-03 22:03:14] local.INFO: Installed plugins: []  
[2025-06-03 22:03:15] local.ERROR: Method App\Filament\Pages\Themes::publishAssets does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method App\\Filament\\Pages\\Themes::publishAssets does not exist. at E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Component.php:136)
[stacktrace]
#0 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(85): Livewire\\Component->__call('publishAssets', Array)
#1 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(25): App\\Filament\\Pages\\Themes->installThemes()
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\Themes->mount()
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#8 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#9 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#10 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#11 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#12 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#13 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#14 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#15 E:\\projects\\My\\code34\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#16 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#17 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#18 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#20 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Themes), '__invoke')
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#77 {main}
"} 
[2025-06-03 22:03:31] local.INFO: Installed plugins: []  
[2025-06-03 22:03:31] local.INFO: Installed plugins: []  
[2025-06-03 22:03:57] local.INFO: Installed plugins: []  
[2025-06-03 22:04:03] local.INFO: Installed plugins: []  
[2025-06-03 22:04:09] local.INFO: Installed plugins: []  
[2025-06-03 22:04:10] local.INFO: Installed plugins: []  
[2025-06-03 22:04:12] local.INFO: Installed plugins: []  
[2025-06-03 22:04:16] local.INFO: Installed plugins: []  
[2025-06-03 22:04:24] local.INFO: Installed plugins: []  
[2025-06-03 22:04:31] local.INFO: Installed plugins: []  
[2025-06-03 22:04:31] local.INFO: Installed plugins: []  
[2025-06-03 22:04:57] local.INFO: Installed plugins: []  
[2025-06-03 22:05:31] local.INFO: Installed plugins: []  
[2025-06-03 22:05:31] local.INFO: Installed plugins: []  
[2025-06-03 22:05:57] local.INFO: Installed plugins: []  
[2025-06-03 22:06:31] local.INFO: Installed plugins: []  
[2025-06-03 22:06:31] local.INFO: Installed plugins: []  
[2025-06-03 22:06:34] local.INFO: Installed plugins: []  
[2025-06-03 22:06:45] local.INFO: Installed plugins: []  
[2025-06-03 22:06:49] local.INFO: Installed plugins: []  
[2025-06-03 22:06:55] local.INFO: Installed plugins: []  
[2025-06-03 22:06:55] local.INFO: Installed plugins: []  
[2025-06-03 22:06:57] local.INFO: Installed plugins: []  
[2025-06-03 22:07:29] local.INFO: Installed plugins: []  
[2025-06-03 22:07:30] local.ERROR: Route [dashboard] not defined. {"view":{"view":"E:\\projects\\My\\code34\\resources\\themes\\code34\\pages\\index.blade.php","data":{"theme":"<pre class=sf-dump id=sf-dump-656487756 data-indent-pad=\"  \"><span class=sf-dump-note>DevDojo\\Themes\\Models\\Theme</span> {<a class=sf-dump-ref>#11163</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"6 characters\">sqlite</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">themes</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Code34</span>\"
    \"<span class=sf-dump-key>folder</span>\" => \"<span class=sf-dump-str title=\"6 characters\">code34</span>\"
    \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str title=\"3 characters\">1.0</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 22:05:06</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 22:06:03</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Code34</span>\"
    \"<span class=sf-dump-key>folder</span>\" => \"<span class=sf-dump-str title=\"6 characters\">code34</span>\"
    \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>version</span>\" => \"<span class=sf-dump-str title=\"3 characters\">1.0</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 22:05:06</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 22:06:03</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">folder</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">version</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-656487756\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#10818</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [dashboard] not defined. at E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 E:\\projects\\My\\code34\\resources\\themes\\code34\\pages\\index.blade.php(17): route('dashboard')
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\projects\\\\My\\\\...')
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\projects\\\\My\\\\...', Array)
#5 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#6 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#8 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#9 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\RequestHandler.php(102): Illuminate\\Support\\Facades\\Facade::__callStatic('toResponse', Array)
#16 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\RequestHandler.php(62): Laravel\\Folio\\RequestHandler->toResponse(Object(Illuminate\\Http\\Request), Object(Laravel\\Folio\\Pipeline\\MatchedView))
#17 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Laravel\\Folio\\RequestHandler->{closure:Laravel\\Folio\\RequestHandler::__invoke():55}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\code34\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\projects\\My\\code34\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\RequestHandler.php(55): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\FolioManager.php(93): Laravel\\Folio\\RequestHandler->__invoke(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Laravel\\Folio\\FolioManager->{closure:Laravel\\Folio\\FolioManager::handler():82}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(244): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(215): Illuminate\\Routing\\Route->runCallable()
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#73 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard] not defined. at E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:517)
[stacktrace]
#0 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('dashboard', Array, true)
#1 E:\\projects\\My\\code34\\storage\\framework\\views\\cfb2f9c448afa80300b1006d09df11d0.php(26): route('dashboard')
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\projects\\\\My\\\\...')
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\projects\\\\My\\\\...', Array)
#5 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#6 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\projects\\\\My\\\\...', Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#8 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\projects\\\\My\\\\...', Array)
#9 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\RequestHandler.php(102): Illuminate\\Support\\Facades\\Facade::__callStatic('toResponse', Array)
#16 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\RequestHandler.php(62): Laravel\\Folio\\RequestHandler->toResponse(Object(Illuminate\\Http\\Request), Object(Laravel\\Folio\\Pipeline\\MatchedView))
#17 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Laravel\\Folio\\RequestHandler->{closure:Laravel\\Folio\\RequestHandler::__invoke():55}(Object(Illuminate\\Http\\Request))
#18 E:\\projects\\My\\code34\\vendor\\alebatistella\\duskapiconf\\src\\Middleware\\ConfigStoreMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): AleBatistella\\DuskApiConf\\Middleware\\ConfigStoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\projects\\My\\code34\\vendor\\ralphjsmit\\livewire-urls\\src\\Middleware\\LivewireUrlsMiddleware.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): RalphJSmit\\Livewire\\Urls\\Middleware\\LivewireUrlsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\RequestHandler.php(55): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\folio\\src\\FolioManager.php(93): Laravel\\Folio\\RequestHandler->__invoke(Object(Illuminate\\Http\\Request))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Laravel\\Folio\\FolioManager->{closure:Laravel\\Folio\\FolioManager::handler():82}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(244): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(215): Illuminate\\Routing\\Route->runCallable()
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#73 {main}
"} 
[2025-06-03 22:07:31] local.INFO: Installed plugins: []  
[2025-06-03 22:07:31] local.INFO: Installed plugins: []  
[2025-06-03 22:07:36] local.INFO: Installed plugins: []  
[2025-06-03 22:07:38] local.INFO: Installed plugins: []  
