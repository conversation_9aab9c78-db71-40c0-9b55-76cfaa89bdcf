[2025-06-03 20:50:46] local.ERROR: <PERSON>rror connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:46] local.INFO: Installed plugins: []  
[2025-06-03 20:50:48] local.ERROR: Error connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:48] local.INFO: Installed plugins: []  
[2025-06-03 20:50:49] local.ERROR: Error connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:49] local.INFO: Installed plugins: []  
[2025-06-03 20:50:51] local.ERROR: Error connecting to database: Database file at path [E:\projects\My\code34\database\database.sqlite] does not exist. Ensure this is an absolute path to the database.  
[2025-06-03 20:50:51] local.INFO: Installed plugins: []  
[2025-06-03 20:50:58] local.INFO: Installed plugins: []  
[2025-06-03 20:51:04] local.INFO: Installed plugins: []  
[2025-06-03 20:51:06] local.INFO: Installed plugins: []  
[2025-06-03 20:51:30] local.INFO: Installed plugins: []  
[2025-06-03 20:52:30] local.INFO: Installed plugins: []  
[2025-06-03 20:52:30] local.INFO: Installed plugins: []  
[2025-06-03 20:53:30] local.INFO: Installed plugins: []  
[2025-06-03 20:53:30] local.INFO: Installed plugins: []  
[2025-06-03 20:54:30] local.INFO: Installed plugins: []  
[2025-06-03 20:55:30] local.INFO: Installed plugins: []  
[2025-06-03 20:55:30] local.INFO: Installed plugins: []  
[2025-06-03 20:56:30] local.INFO: Installed plugins: []  
[2025-06-03 20:56:30] local.INFO: Installed plugins: []  
[2025-06-03 20:57:30] local.INFO: Installed plugins: []  
[2025-06-03 20:57:30] local.INFO: Installed plugins: []  
[2025-06-03 20:58:30] local.INFO: Installed plugins: []  
[2025-06-03 20:58:30] local.INFO: Installed plugins: []  
[2025-06-03 20:58:30] local.INFO: Installed plugins: []  
[2025-06-03 20:59:30] local.INFO: Installed plugins: []  
[2025-06-03 21:00:30] local.INFO: Installed plugins: []  
[2025-06-03 21:01:30] local.INFO: Installed plugins: []  
[2025-06-03 21:01:30] local.INFO: Installed plugins: []  
[2025-06-03 21:02:30] local.INFO: Installed plugins: []  
[2025-06-03 21:02:30] local.INFO: Installed plugins: []  
[2025-06-03 21:03:30] local.INFO: Installed plugins: []  
[2025-06-03 21:03:30] local.INFO: Installed plugins: []  
[2025-06-03 21:04:30] local.INFO: Installed plugins: []  
[2025-06-03 21:05:30] local.INFO: Installed plugins: []  
[2025-06-03 21:05:30] local.INFO: Installed plugins: []  
[2025-06-03 21:06:30] local.INFO: Installed plugins: []  
[2025-06-03 21:06:30] local.INFO: Installed plugins: []  
[2025-06-03 21:07:30] local.INFO: Installed plugins: []  
[2025-06-03 21:07:30] local.INFO: Installed plugins: []  
[2025-06-03 21:08:30] local.INFO: Installed plugins: []  
[2025-06-03 21:08:30] local.INFO: Installed plugins: []  
[2025-06-03 21:08:30] local.INFO: Installed plugins: []  
[2025-06-03 21:09:30] local.INFO: Installed plugins: []  
[2025-06-03 21:09:30] local.INFO: Installed plugins: []  
[2025-06-03 21:10:30] local.INFO: Installed plugins: []  
[2025-06-03 21:10:30] local.INFO: Installed plugins: []  
[2025-06-03 21:11:30] local.INFO: Installed plugins: []  
[2025-06-03 21:12:30] local.INFO: Installed plugins: []  
[2025-06-03 21:12:30] local.INFO: Installed plugins: []  
[2025-06-03 21:13:30] local.INFO: Installed plugins: []  
[2025-06-03 21:13:30] local.INFO: Installed plugins: []  
[2025-06-03 21:14:30] local.INFO: Installed plugins: []  
[2025-06-03 21:14:30] local.INFO: Installed plugins: []  
[2025-06-03 21:15:30] local.INFO: Installed plugins: []  
[2025-06-03 21:16:30] local.INFO: Installed plugins: []  
[2025-06-03 21:16:30] local.INFO: Installed plugins: []  
[2025-06-03 21:17:30] local.INFO: Installed plugins: []  
[2025-06-03 21:17:30] local.INFO: Installed plugins: []  
[2025-06-03 21:18:30] local.INFO: Installed plugins: []  
[2025-06-03 21:18:30] local.INFO: Installed plugins: []  
[2025-06-03 21:18:30] local.INFO: Installed plugins: []  
[2025-06-03 21:19:30] local.INFO: Installed plugins: []  
[2025-06-03 21:19:30] local.INFO: Installed plugins: []  
[2025-06-03 21:20:30] local.INFO: Installed plugins: []  
[2025-06-03 21:20:43] local.INFO: Installed plugins: []  
[2025-06-03 21:21:03] local.INFO: Installed plugins: []  
[2025-06-03 21:21:16] local.INFO: Installed plugins: []  
[2025-06-03 21:21:17] local.INFO: Installed plugins: []  
[2025-06-03 21:21:30] local.INFO: Installed plugins: []  
[2025-06-03 21:21:38] local.INFO: Installed plugins: []  
[2025-06-03 21:21:39] local.INFO: Installed plugins: []  
[2025-06-03 21:21:40] local.INFO: Installed plugins: []  
[2025-06-03 21:21:52] local.INFO: Installed plugins: []  
[2025-06-03 21:21:53] local.INFO: Installed plugins: []  
[2025-06-03 21:21:55] local.INFO: Installed plugins: []  
[2025-06-03 21:22:16] local.INFO: Installed plugins: []  
[2025-06-03 21:22:18] local.INFO: Installed plugins: []  
[2025-06-03 21:22:30] local.INFO: Installed plugins: []  
[2025-06-03 21:23:30] local.INFO: Installed plugins: []  
[2025-06-03 21:24:30] local.INFO: Installed plugins: []  
[2025-06-03 21:24:30] local.INFO: Installed plugins: []  
[2025-06-03 21:25:25] local.INFO: Installed plugins: []  
[2025-06-03 21:25:30] local.INFO: Installed plugins: []  
[2025-06-03 21:25:31] local.INFO: Installed plugins: []  
[2025-06-03 21:25:31] local.INFO: Installed plugins: []  
[2025-06-03 21:25:33] local.INFO: Installed plugins: []  
[2025-06-03 21:25:35] local.INFO: Installed plugins: []  
[2025-06-03 21:25:51] local.INFO: Installed plugins: []  
[2025-06-03 21:25:52] local.INFO: Installed plugins: []  
[2025-06-03 21:25:52] local.INFO: Installed plugins: []  
[2025-06-03 21:26:02] local.INFO: Installed plugins: []  
[2025-06-03 21:26:07] local.INFO: Installed plugins: []  
[2025-06-03 21:26:08] local.INFO: Installed plugins: []  
[2025-06-03 21:26:08] local.INFO: Installed plugins: []  
[2025-06-03 21:26:15] local.INFO: Installed plugins: []  
[2025-06-03 21:26:16] local.INFO: Installed plugins: []  
[2025-06-03 21:26:21] local.INFO: Installed plugins: []  
[2025-06-03 21:26:30] local.INFO: Installed plugins: []  
[2025-06-03 21:27:30] local.INFO: Installed plugins: []  
[2025-06-03 21:27:30] local.INFO: Installed plugins: []  
[2025-06-03 21:28:30] local.INFO: Installed plugins: []  
[2025-06-03 21:28:31] local.INFO: Installed plugins: []  
[2025-06-03 21:28:31] local.INFO: Installed plugins: []  
[2025-06-03 21:29:30] local.INFO: Installed plugins: []  
[2025-06-03 21:29:30] local.INFO: Installed plugins: []  
[2025-06-03 21:30:30] local.INFO: Installed plugins: []  
[2025-06-03 21:30:30] local.INFO: Installed plugins: []  
[2025-06-03 21:31:30] local.INFO: Installed plugins: []  
[2025-06-03 21:31:30] local.INFO: Installed plugins: []  
[2025-06-03 21:32:30] local.INFO: Installed plugins: []  
[2025-06-03 21:33:30] local.INFO: Installed plugins: []  
[2025-06-03 21:33:30] local.INFO: Installed plugins: []  
[2025-06-03 21:34:30] local.INFO: Installed plugins: []  
[2025-06-03 21:34:30] local.INFO: Installed plugins: []  
[2025-06-03 21:35:30] local.INFO: Installed plugins: []  
[2025-06-03 21:36:30] local.INFO: Installed plugins: []  
[2025-06-03 21:36:30] local.INFO: Installed plugins: []  
[2025-06-03 21:37:30] local.INFO: Installed plugins: []  
[2025-06-03 21:37:30] local.INFO: Installed plugins: []  
[2025-06-03 21:38:30] local.INFO: Installed plugins: []  
[2025-06-03 21:38:30] local.INFO: Installed plugins: []  
[2025-06-03 21:39:30] local.INFO: Installed plugins: []  
[2025-06-03 21:39:30] local.INFO: Installed plugins: []  
[2025-06-03 21:40:30] local.INFO: Installed plugins: []  
[2025-06-03 21:40:30] local.INFO: Installed plugins: []  
[2025-06-03 21:41:30] local.INFO: Installed plugins: []  
[2025-06-03 21:41:30] local.INFO: Installed plugins: []  
[2025-06-03 21:42:31] local.INFO: Installed plugins: []  
[2025-06-03 21:42:31] local.INFO: Installed plugins: []  
[2025-06-03 21:43:31] local.INFO: Installed plugins: []  
[2025-06-03 21:44:30] local.INFO: Installed plugins: []  
[2025-06-03 21:45:31] local.INFO: Installed plugins: []  
[2025-06-03 21:45:31] local.INFO: Installed plugins: []  
[2025-06-03 21:46:31] local.INFO: Installed plugins: []  
[2025-06-03 21:47:31] local.INFO: Installed plugins: []  
[2025-06-03 21:48:30] local.INFO: Installed plugins: []  
[2025-06-03 21:48:31] local.INFO: Installed plugins: []  
[2025-06-03 21:48:31] local.INFO: Installed plugins: []  
[2025-06-03 21:49:31] local.INFO: Installed plugins: []  
[2025-06-03 21:50:31] local.INFO: Installed plugins: []  
[2025-06-03 21:51:31] local.INFO: Installed plugins: []  
[2025-06-03 21:52:31] local.INFO: Installed plugins: []  
[2025-06-03 21:52:31] local.INFO: Installed plugins: []  
[2025-06-03 21:53:31] local.INFO: Installed plugins: []  
[2025-06-03 21:53:31] local.INFO: Installed plugins: []  
[2025-06-03 21:54:31] local.INFO: Installed plugins: []  
[2025-06-03 21:54:31] local.INFO: Installed plugins: []  
[2025-06-03 21:55:31] local.INFO: Installed plugins: []  
[2025-06-03 21:55:31] local.INFO: Installed plugins: []  
[2025-06-03 21:56:31] local.INFO: Installed plugins: []  
[2025-06-03 21:56:31] local.INFO: Installed plugins: []  
[2025-06-03 21:57:31] local.INFO: Installed plugins: []  
[2025-06-03 21:57:31] local.INFO: Installed plugins: []  
[2025-06-03 21:58:30] local.INFO: Installed plugins: []  
[2025-06-03 21:58:31] local.INFO: Installed plugins: []  
[2025-06-03 21:58:31] local.INFO: Installed plugins: []  
[2025-06-03 21:59:31] local.INFO: Installed plugins: []  
[2025-06-03 21:59:31] local.INFO: Installed plugins: []  
[2025-06-03 21:59:42] local.INFO: Installed plugins: []  
[2025-06-03 22:00:31] local.INFO: Installed plugins: []  
[2025-06-03 22:00:36] local.INFO: Installed plugins: []  
[2025-06-03 22:00:49] local.INFO: Installed plugins: []  
[2025-06-03 22:00:57] local.INFO: Installed plugins: []  
[2025-06-03 22:01:04] local.INFO: Installed plugins: []  
[2025-06-03 22:01:11] local.INFO: Installed plugins: []  
[2025-06-03 22:01:11] local.INFO: Installed plugins: []  
[2025-06-03 22:01:12] local.INFO: Installed plugins: []  
[2025-06-03 22:01:13] local.ERROR: Method App\Filament\Pages\Themes::publishAssets does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method App\\Filament\\Pages\\Themes::publishAssets does not exist. at E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Component.php:136)
[stacktrace]
#0 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(85): Livewire\\Component->__call('publishAssets', Array)
#1 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(25): App\\Filament\\Pages\\Themes->installThemes()
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\Themes->mount()
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#8 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#9 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#10 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#11 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#12 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#13 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#14 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#15 E:\\projects\\My\\code34\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#16 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#17 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#18 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#20 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Themes), '__invoke')
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#77 {main}
"} 
[2025-06-03 22:01:15] local.INFO: Installed plugins: []  
[2025-06-03 22:01:20] local.INFO: Installed plugins: []  
[2025-06-03 22:01:24] local.INFO: Installed plugins: []  
[2025-06-03 22:01:31] local.INFO: Installed plugins: []  
[2025-06-03 22:01:31] local.INFO: Installed plugins: []  
[2025-06-03 22:01:42] local.ERROR: Maximum execution time of 30 seconds exceeded {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at E:\\projects\\My\\code34\\storage\\framework\\views\\5e8c24f9e8e354bfded7ce03625b600f.php:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 22:01:45] local.INFO: Installed plugins: []  
[2025-06-03 22:01:47] local.INFO: Installed plugins: []  
[2025-06-03 22:02:30] local.INFO: Installed plugins: []  
[2025-06-03 22:02:31] local.INFO: Installed plugins: []  
[2025-06-03 22:02:41] local.INFO: Installed plugins: []  
[2025-06-03 22:02:42] local.INFO: Installed plugins: []  
[2025-06-03 22:02:44] local.INFO: Installed plugins: []  
[2025-06-03 22:03:03] local.INFO: Installed plugins: []  
[2025-06-03 22:03:03] local.INFO: Installed plugins: []  
[2025-06-03 22:03:04] local.ERROR: Method App\Filament\Pages\Themes::publishAssets does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method App\\Filament\\Pages\\Themes::publishAssets does not exist. at E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Component.php:136)
[stacktrace]
#0 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(85): Livewire\\Component->__call('publishAssets', Array)
#1 E:\\projects\\My\\code34\\app\\Filament\\Pages\\Themes.php(25): App\\Filament\\Pages\\Themes->installThemes()
#2 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Filament\\Pages\\Themes->mount()
#3 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#4 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(134): Livewire\\Wrapped->__call('mount', Array)
#8 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php(20): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->callHook('mount', Array)
#9 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHook.php(19): Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks->mount(Array, false)
#10 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php(45): Livewire\\ComponentHook->callMount(Array, false)
#11 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\EventBus.php(60): Livewire\\ComponentHookRegistry::{closure:Livewire\\ComponentHookRegistry::boot():39}(Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#12 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\helpers.php(98): Livewire\\EventBus->trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#13 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(50): Livewire\\trigger('mount', Object(App\\Filament\\Pages\\Themes), Array, NULL, false)
#14 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#15 E:\\projects\\My\\code34\\vendor\\livewire\\volt\\src\\LivewireManager.php(23): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array, NULL)
#16 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(17): Livewire\\Volt\\LivewireManager->mount('App\\\\Filament\\\\Pa...', Array)
#17 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#18 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#19 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): Livewire\\Component->__invoke()
#20 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Pages\\Themes), '__invoke')
#21 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#22 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#24 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#25 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#27 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#29 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#31 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#33 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#35 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#37 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#39 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#42 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#44 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#46 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#47 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#48 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#54 E:\\projects\\My\\code34\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#55 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\projects\\My\\code34\\vendor\\filament\\filament\\src\\Http\\Middleware\\DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#57 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#59 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#62 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#65 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#67 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#69 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#71 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#73 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 E:\\projects\\My\\code34\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 E:\\projects\\My\\code34\\public\\index.php(65): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\projects\\\\My\\\\...')
#77 {main}
"} 
[2025-06-03 22:03:05] local.INFO: Installed plugins: []  
