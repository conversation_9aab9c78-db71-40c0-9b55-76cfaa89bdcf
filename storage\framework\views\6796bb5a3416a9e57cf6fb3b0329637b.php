<?php
    use function <PERSON>vel\Folio\{name};
    name('home');
?>

<?php if (isset($component)) { $__componentOriginalf103f87f9e6975b672a2453f5462c100 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf103f87f9e6975b672a2453f5462c100 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '2ff845dbd85fb3c77d7f45d510895784::layouts.marketing','data' => ['seo' => [
        'title'         => setting('site.title', 'Laravel Wave'),
        'description'   => setting('site.description', 'Software as a Service Starter Kit'),
        'image'         => url('/og_image.png'),
        'type'          => 'website'
    ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.marketing'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['seo' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        'title'         => setting('site.title', 'Laravel Wave'),
        'description'   => setting('site.description', 'Software as a Service Starter Kit'),
        'image'         => url('/og_image.png'),
        'type'          => 'website'
    ])]); ?>
        
        <?php if (isset($component)) { $__componentOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '2ff845dbd85fb3c77d7f45d510895784::marketing.sections.hero','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('marketing.sections.hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45)): ?>
<?php $attributes = $__attributesOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45; ?>
<?php unset($__attributesOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45)): ?>
<?php $component = $__componentOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45; ?>
<?php unset($__componentOriginal5bfea2561bd1e5c6a1f3aeedb0eefa45); ?>
<?php endif; ?>
        
        <?php if (isset($component)) { $__componentOriginala766c2d312d6f7864fe218e2500d2bba = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala766c2d312d6f7864fe218e2500d2bba = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '5a9dc566c9f7b847570a7598d6929410::container','data' => ['class' => 'py-12 border-t sm:py-24 border-zinc-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'py-12 border-t sm:py-24 border-zinc-200']); ?>
            <?php if (isset($component)) { $__componentOriginalecb281f39124d6c7c028a36d3c68ca3e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalecb281f39124d6c7c028a36d3c68ca3e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '2ff845dbd85fb3c77d7f45d510895784::marketing.sections.features','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('marketing.sections.features'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalecb281f39124d6c7c028a36d3c68ca3e)): ?>
<?php $attributes = $__attributesOriginalecb281f39124d6c7c028a36d3c68ca3e; ?>
<?php unset($__attributesOriginalecb281f39124d6c7c028a36d3c68ca3e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalecb281f39124d6c7c028a36d3c68ca3e)): ?>
<?php $component = $__componentOriginalecb281f39124d6c7c028a36d3c68ca3e; ?>
<?php unset($__componentOriginalecb281f39124d6c7c028a36d3c68ca3e); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala766c2d312d6f7864fe218e2500d2bba)): ?>
<?php $attributes = $__attributesOriginala766c2d312d6f7864fe218e2500d2bba; ?>
<?php unset($__attributesOriginala766c2d312d6f7864fe218e2500d2bba); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala766c2d312d6f7864fe218e2500d2bba)): ?>
<?php $component = $__componentOriginala766c2d312d6f7864fe218e2500d2bba; ?>
<?php unset($__componentOriginala766c2d312d6f7864fe218e2500d2bba); ?>
<?php endif; ?>

        <?php if (isset($component)) { $__componentOriginala766c2d312d6f7864fe218e2500d2bba = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala766c2d312d6f7864fe218e2500d2bba = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '5a9dc566c9f7b847570a7598d6929410::container','data' => ['class' => 'py-12 border-t sm:py-24 border-zinc-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'py-12 border-t sm:py-24 border-zinc-200']); ?>
            <?php if (isset($component)) { $__componentOriginalb6577a032af78af16c1e602d850a1a92 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb6577a032af78af16c1e602d850a1a92 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '2ff845dbd85fb3c77d7f45d510895784::marketing.sections.testimonials','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('marketing.sections.testimonials'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb6577a032af78af16c1e602d850a1a92)): ?>
<?php $attributes = $__attributesOriginalb6577a032af78af16c1e602d850a1a92; ?>
<?php unset($__attributesOriginalb6577a032af78af16c1e602d850a1a92); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb6577a032af78af16c1e602d850a1a92)): ?>
<?php $component = $__componentOriginalb6577a032af78af16c1e602d850a1a92; ?>
<?php unset($__componentOriginalb6577a032af78af16c1e602d850a1a92); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala766c2d312d6f7864fe218e2500d2bba)): ?>
<?php $attributes = $__attributesOriginala766c2d312d6f7864fe218e2500d2bba; ?>
<?php unset($__attributesOriginala766c2d312d6f7864fe218e2500d2bba); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala766c2d312d6f7864fe218e2500d2bba)): ?>
<?php $component = $__componentOriginala766c2d312d6f7864fe218e2500d2bba; ?>
<?php unset($__componentOriginala766c2d312d6f7864fe218e2500d2bba); ?>
<?php endif; ?>
        
        <?php if (isset($component)) { $__componentOriginala766c2d312d6f7864fe218e2500d2bba = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala766c2d312d6f7864fe218e2500d2bba = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '5a9dc566c9f7b847570a7598d6929410::container','data' => ['class' => 'py-12 border-t sm:py-24 border-zinc-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('container'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'py-12 border-t sm:py-24 border-zinc-200']); ?>
            <?php if (isset($component)) { $__componentOriginal141ce504b3e68af8635389c74d9cc3be = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal141ce504b3e68af8635389c74d9cc3be = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '2ff845dbd85fb3c77d7f45d510895784::marketing.sections.pricing','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('marketing.sections.pricing'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal141ce504b3e68af8635389c74d9cc3be)): ?>
<?php $attributes = $__attributesOriginal141ce504b3e68af8635389c74d9cc3be; ?>
<?php unset($__attributesOriginal141ce504b3e68af8635389c74d9cc3be); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal141ce504b3e68af8635389c74d9cc3be)): ?>
<?php $component = $__componentOriginal141ce504b3e68af8635389c74d9cc3be; ?>
<?php unset($__componentOriginal141ce504b3e68af8635389c74d9cc3be); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala766c2d312d6f7864fe218e2500d2bba)): ?>
<?php $attributes = $__attributesOriginala766c2d312d6f7864fe218e2500d2bba; ?>
<?php unset($__attributesOriginala766c2d312d6f7864fe218e2500d2bba); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala766c2d312d6f7864fe218e2500d2bba)): ?>
<?php $component = $__componentOriginala766c2d312d6f7864fe218e2500d2bba; ?>
<?php unset($__componentOriginala766c2d312d6f7864fe218e2500d2bba); ?>
<?php endif; ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf103f87f9e6975b672a2453f5462c100)): ?>
<?php $attributes = $__attributesOriginalf103f87f9e6975b672a2453f5462c100; ?>
<?php unset($__attributesOriginalf103f87f9e6975b672a2453f5462c100); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf103f87f9e6975b672a2453f5462c100)): ?>
<?php $component = $__componentOriginalf103f87f9e6975b672a2453f5462c100; ?>
<?php unset($__componentOriginalf103f87f9e6975b672a2453f5462c100); ?>
<?php endif; ?>
<?php /**PATH E:\projects\My\code34\resources\themes\anchor\pages\index.blade.php ENDPATH**/ ?>