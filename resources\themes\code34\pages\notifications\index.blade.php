<?php
    use function <PERSON>vel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    name('notifications');
    middleware('auth');

    new class extends Component{

        public $notifications_count;
        public $unreadNotifications;

        public function mount(){
            $this->updateNotifications();
        }

        public function delete($id){
            $notification = auth()->user()->notifications()->where('id', $id)->first();
            if ($notification){
                $notification->delete();
            }
            $this->updateNotifications();
        }

        public function updateNotifications(){
            $this->setUnreadNotifications = $this->unreadNotifications = auth()->user()->unreadNotifications->all();
            $this->notifications_count = auth()->user()->unreadNotifications->count();
        }
    }
?>

<x-layouts.app>
    @volt('notifications')
        <div class="container mt-5 pt-5">
            <div class="row">
                <div class="col-lg-12">
                    <x-elements.back-button
                        text="Voltar ao Dashboard"
                        :href="route('dashboard')"
                        class="mb-4"
                    />

                    <div class="hero-heading-sec text-center mb-5">
                        <h2 class="text-white">
                            <span>Notificações</span>
                        </h2>
                        <p class="text-white">Veja suas notificações e atualizações importantes.</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12">
                    <div class="bg-white rounded p-4">
                        @forelse ($unreadNotifications as $index => $notification)
                            @php $notification_data = (object)$notification->data; @endphp
                            <div id="notification-li-{{ $index + 1 }}" class="d-flex flex-column pb-3 @if(!$loop->last){{ 'border-bottom' }}@endif mb-3">

                                <a href="{{ @$notification_data->link }}" class="d-flex align-items-start p-3 text-decoration-none">
                                    <div class="flex-shrink-0 pt-1">
                                        <img class="rounded-circle" src="{{ @$notification_data->icon }}" alt="" style="width: 40px; height: 40px;">
                                    </div>
                                    <div class="flex-fill ml-3">
                                        <div class="d-flex align-items-center mb-1">
                                            <p class="mb-0 font-weight-bold text-dark">{{ @$notification_data->user['name'] }}</p>
                                            <small class="text-muted ml-2">{{ \Carbon\Carbon::parse(@$notification->created_at)->format('d/m/Y H:i') }}</small>
                                        </div>
                                        <p class="mb-0 text-muted">{{ @$notification_data->body }} em <span class="text-primary">{{ @$notification_data->title }}</span></p>
                                    </div>
                                </a>
                                <span wire:click="delete('{{ $notification->id }}')" class="btn btn-sm btn-outline-success ml-5 align-self-start" style="cursor: pointer;">
                                    <i class="fas fa-check mr-1"></i>
                                    Marcar como Lida
                                </span>

                            </div>
                        @empty
                            <div class="@if($notifications_count > 0){{ 'd-none' }}@endif d-flex align-items-center justify-content-center bg-light rounded p-5 text-muted">
                                <i class="fas fa-bell-slash mr-2"></i>
                                Nenhuma Notificação
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    @endvolt

</x-layouts.app>
