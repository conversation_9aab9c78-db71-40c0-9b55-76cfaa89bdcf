<?php
    use function <PERSON><PERSON>\Folio\{middleware, name};
    middleware('auth');
    name('settings');
?>

<x-layouts.app>
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-lg-12">
                <x-elements.back-button
                    text="Voltar ao Dashboard"
                    :href="route('dashboard')"
                    class="mb-4"
                />

                <div class="hero-heading-sec text-center mb-5">
                    <h2 class="text-white">
                        <span>Configurações</span>
                    </h2>
                    <p class="text-white">Gerencie suas configurações e preferências da conta.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-user-edit fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Perfil</h4>
                        <p>Edite suas informações pessoais e configurações de perfil.</p>
                        <a href="{{ route('settings.profile') }}" class="btn-main bg-btn lnk mt-3">
                            Editar Perfil <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-shield-alt fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Segurança</h4>
                        <p>Gerencie sua senha e configurações de segurança.</p>
                        <a href="{{ route('settings.security') }}" class="btn-main bg-btn lnk mt-3">
                            Configurar <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-credit-card fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Assinatura</h4>
                        <p>Gerencie sua assinatura e métodos de pagamento.</p>
                        <a href="{{ route('settings.subscription') }}" class="btn-main bg-btn lnk mt-3">
                            Ver Assinatura <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-key fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>API</h4>
                        <p>Gerencie suas chaves de API e integrações.</p>
                        <a href="{{ route('settings.api') }}" class="btn-main bg-btn lnk mt-3">
                            Configurar API <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-lg-12">
                <div class="bg-white rounded p-4">
                    <h5 class="mb-3">Informações da Conta</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Nome:</strong> {{ auth()->user()->name }}</p>
                            <p><strong>Email:</strong> {{ auth()->user()->email }}</p>
                            <p><strong>Username:</strong> {{ '@' . auth()->user()->username }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Função:</strong> {{ auth()->user()->roles()->first()->name ?? 'Usuário' }}</p>
                            <p><strong>Membro desde:</strong> {{ auth()->user()->created_at->format('d/m/Y') }}</p>
                            <p><strong>Última atualização:</strong> {{ auth()->user()->updated_at->format('d/m/Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
