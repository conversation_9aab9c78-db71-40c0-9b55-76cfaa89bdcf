<?php
    use function <PERSON><PERSON>\Folio\{middleware, name};
	middleware('auth');
    name('dashboard');
?>

<x-layouts.app>
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-lg-12">
                <div class="hero-heading-sec text-center mb-5">
                    <h2 class="text-white">
                        <span>Dashboard</span>
                        <span>{{ auth()->user()->name }}</span>
                    </h2>
                    <p class="text-white">Bem-vindo ao seu painel de controle. Gerencie suas configurações e acesse recursos.</p>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-user-cog fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Perfil</h4>
                        <p>Gerencie suas informações pessoais e configurações de conta.</p>
                        <a href="{{ route('profile', auth()->user()->username) }}" class="btn-main bg-btn lnk mt-3">
                            Ver Perfil <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-cog fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Configurações</h4>
                        <p>Ajuste suas preferências e configurações de segurança.</p>
                        <a href="{{ route('settings') }}" class="btn-main bg-btn lnk mt-3">
                            Configurações <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-bell fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Notificações</h4>
                        <p>Veja suas notificações e atualizações importantes.</p>
                        <a href="{{ route('notifications') }}" class="btn-main bg-btn lnk mt-3">
                            Ver Notificações <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6 mb-4">
                <div class="service-card-app home-services gradient-border bg-white">
                    <div class="services-img-div">
                        <div class="service-card-img">
                            <i class="fas fa-credit-card fa-3x text-primary"></i>
                        </div>
                    </div>
                    <div class="service-card-content">
                        <h4>Assinatura</h4>
                        <p>Gerencie sua assinatura e planos disponíveis.</p>
                        <a href="{{ route('settings.subscription') }}" class="btn-main bg-btn lnk mt-3">
                            Ver Assinatura <i class="fas fa-chevron-right fa-icon"></i>
                            <span class="circle"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-lg-12">
                <div class="bg-white p-4 rounded">
                    <h5>Informações da Conta</h5>
                    <p><strong>Nome:</strong> {{ auth()->user()->name }}</p>
                    <p><strong>Email:</strong> {{ auth()->user()->email }}</p>
                    <p><strong>Função:</strong> {{ auth()->user()->roles()->first()->name ?? 'Usuário' }}</p>
                    
                    @subscriber
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Você é um usuário assinante com a função <strong>{{ auth()->user()->roles()->first()->name }}</strong>.
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Você tem a função <strong>{{ auth()->user()->roles()->first()->name }}</strong>. Para fazer upgrade, <a href="{{ route('settings.subscription') }}" class="underline">assine um plano</a>.
                        </div>
                    @endsubscriber
                    
                    @admin
                        <div class="alert alert-warning">
                            <i class="fas fa-crown"></i> Você tem privilégios de administrador. <a href="/admin" class="underline">Acessar painel administrativo</a>.
                        </div>
                    @endadmin
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
