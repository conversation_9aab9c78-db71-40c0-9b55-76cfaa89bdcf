<?php
    use function <PERSON><PERSON>\Folio\{middleware, name};
    middleware('auth');
    name('settings.api');
?>

<x-layouts.app>
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-lg-12">
                <x-elements.back-button
                    text="Voltar às Configurações"
                    :href="route('settings')"
                    class="mb-4"
                />

                <div class="hero-heading-sec text-center mb-5">
                    <h2 class="text-white">
                        <span>Configurações de API</span>
                    </h2>
                    <p class="text-white">Gerencie suas chaves de API e integrações.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="bg-white rounded p-5">
                    <h5 class="mb-4">Chaves de API</h5>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Sobre as Chaves de API</strong><br>
                        Use as chaves de API para integrar nossa plataforma com suas aplicações. Mantenha suas chaves seguras e não as compartilhe.
                    </div>

                    @if(auth()->user()->tokens->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>Última Utilização</th>
                                        <th>Criada em</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(auth()->user()->tokens as $token)
                                        <tr>
                                            <td>{{ $token->name }}</td>
                                            <td>{{ $token->last_used_at ? $token->last_used_at->format('d/m/Y H:i') : 'Nunca' }}</td>
                                            <td>{{ $token->created_at->format('d/m/Y H:i') }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-danger" onclick="alert('Funcionalidade em desenvolvimento')">
                                                    <i class="fas fa-trash"></i> Revogar
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-key fa-3x text-muted mb-3"></i>
                            <h6>Nenhuma Chave de API</h6>
                            <p class="text-muted">Você ainda não criou nenhuma chave de API.</p>
                        </div>
                    @endif

                    <div class="text-center mt-4">
                        <button class="btn-main bg-btn lnk" onclick="alert('Funcionalidade em desenvolvimento')">
                            <i class="fas fa-plus fa-icon"></i> Criar Nova Chave
                            <span class="circle"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-10 mx-auto">
                <div class="bg-white rounded p-5">
                    <h5 class="mb-4">Documentação da API</h5>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-book fa-2x text-primary mb-3"></i>
                                    <h6>Documentação</h6>
                                    <p class="text-muted">Acesse a documentação completa da nossa API.</p>
                                    <button class="btn btn-outline-primary" onclick="alert('Documentação em desenvolvimento')">
                                        Ver Docs
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-code fa-2x text-success mb-3"></i>
                                    <h6>Exemplos</h6>
                                    <p class="text-muted">Veja exemplos de código em diferentes linguagens.</p>
                                    <button class="btn btn-outline-success" onclick="alert('Exemplos em desenvolvimento')">
                                        Ver Exemplos
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-10 mx-auto">
                <div class="bg-white rounded p-4">
                    <h6>Precisa de Ajuda com a API?</h6>
                    <p class="text-muted">Se você tiver dúvidas sobre nossa API ou precisar de suporte técnico, entre em contato conosco.</p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                        <i class="fas fa-envelope mr-2"></i> Suporte Técnico
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
